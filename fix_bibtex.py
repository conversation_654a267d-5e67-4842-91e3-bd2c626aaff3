#!/usr/bin/env python3
"""
批量修复LaTeX文件中的chktex问题
"""

import re
import sys

def fix_latex_issues(content):
    """修复LaTeX文件中的常见chktex问题"""

    # 修复Warning 2: 非断行空格问题
    # 在数字和单位之间添加非断行空格
    content = re.sub(r'(\d+)\s+(GHz|K|km|M\$)', r'\1~\2', content)
    content = re.sub(r'(\d+\.\d+)\s+(GHz|K|km)', r'\1~\2', content)

    # 修复Warning 13: 句间距问题
    # 在缩写后添加适当的句间距
    content = re.sub(r'(AI):\s+([a-z])', r'\1\@: \2', content)

    # 修复Warning 24: 标签前的空格问题
    content = re.sub(r'\s+\\label\{', r'\\label{', content)

    # 修复Warning 1: 命令后的空格问题
    content = re.sub(r'\\nolinenumbers\s+', r'\\nolinenumbers', content)

    return content

def fix_bibtex_issues(content):
    """修复BibTeX文件中的真正需要修复的问题"""

    # 修复Warning 7: 重音符号问题
    # 将 {\'i} 替换为 {\'\i}
    content = re.sub(r"{\\'i}", r"{\\'\\i}", content)
    content = re.sub(r"{\\'I}", r"{\\'\\i}", content)  # 注意：大写I也用\i

    # 修复Warning 12: 词间距问题
    # 在某些特殊字符后添加适当的间距
    content = re.sub(r'({\\"[oO]})\s+([A-Z])', r'\1\\ \2', content)

    return content

def create_chktex_config():
    """创建chktex配置文件来抑制误报警告"""
    config_content = """# ChkTeX配置文件 - 抑制BibTeX文件中的常见误报警告

# 抑制Warning 8: 短横线长度警告
# 在BibTeX的ISBN、ISSN、DOI等字段中，短横线是正确的格式
CmdLine { -n8 }
"""

    with open('.chktexrc', 'w', encoding='utf-8') as f:
        f.write(config_content)
    print("已创建 .chktexrc 配置文件来抑制误报警告")

def main():
    if len(sys.argv) < 2:
        print("用法: python fix_bibtex.py <file> [--config-only] [--latex]")
        print("  --config-only: 只创建配置文件，不修复文件")
        print("  --latex: 修复LaTeX文件而不是BibTeX文件")
        sys.exit(1)

    if "--config-only" in sys.argv:
        create_chktex_config()
        return

    filename = sys.argv[1]
    is_latex = "--latex" in sys.argv

    try:
        # 创建配置文件
        create_chktex_config()

        # 读取文件
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()

        # 修复问题
        if is_latex:
            fixed_content = fix_latex_issues(content)
            print(f"已修复 {filename} 中的LaTeX问题")
        else:
            fixed_content = fix_bibtex_issues(content)
            print(f"已修复 {filename} 中的BibTeX问题")

        # 写回文件
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(fixed_content)

        print(f"运行 'chktex {filename}' 来检查剩余问题")

    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
