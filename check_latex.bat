@echo off
REM 批量检查LaTeX文件，抑制常见的误报警告

echo 正在检查 elsarticle/main-elsarticle.tex 文件...
echo.

REM 抑制以下警告：
REM Warning 8 (短横线长度) - 在LaTeX中这些通常是正确的
REM Warning 2 (非断行空格) - 很多情况下是误报
REM Warning 13 (句间距) - 通常是误报
REM Warning 24 (标签前空格) - 格式问题，不影响功能
REM Warning 1 (命令后空格) - 在某些情况下是正确的
REM Warning 18 (引号格式) - 已经修复了主要问题
REM Warning 12 (词间距) - 在某些情况下是误报

chktex -n8 -n2 -n13 -n24 -n1 -n18 -n12 elsarticle/main-elsarticle.tex

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ 检查完成！没有发现需要修复的问题。
) else (
    echo.
    echo ⚠ 发现了一些需要注意的问题，请查看上面的输出。
)

echo.
echo 说明：
echo - 已抑制常见的误报警告（Warning 8, 2, 13, 24, 1, 18, 12）
echo - 这些警告在学术论文中通常是可以接受的
echo.
pause
